import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { readFileSync } from 'fs';
import { join } from 'path';

async function runMigration() {
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
  }

  console.log('Connecting to database...');
  const sql = postgres(databaseUrl);
  const db = drizzle(sql);

  try {
    console.log('Reading migration file...');
    const migrationPath = join(__dirname, 'migrations', 'add-identity-verification.sql');
    const migrationSql = readFileSync(migrationPath, 'utf-8');

    console.log('Running migration...');
    
    // Split the migration into individual statements and execute them
    const statements = migrationSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        await sql.unsafe(statement);
      }
    }

    console.log('✅ Migration completed successfully!');
    
    // Verify the changes
    console.log('\nVerifying migration...');
    const result = await sql`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('identity_verified', 'identity_verification_status', 'trust_score')
      ORDER BY column_name;
    `;
    
    console.log('New columns added:');
    console.table(result);

    // Check if any users exist and their trust scores
    const userCount = await sql`SELECT COUNT(*) as count FROM users`;
    console.log(`\nTotal users in database: ${userCount[0].count}`);

    if (parseInt(userCount[0].count) > 0) {
      const sampleUsers = await sql`
        SELECT id, username, identity_verified, identity_verification_status, trust_score 
        FROM users 
        LIMIT 5
      `;
      console.log('\nSample users with new fields:');
      console.table(sampleUsers);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sql.end();
    console.log('Database connection closed.');
  }
}

runMigration().catch(console.error);
