/* Carousel Styles */
.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  transition: transform 0.3s ease-in-out;
}

.carousel-item {
  flex-shrink: 0;
  padding: 0 4px;
  height: auto; /* Allow height to adjust based on content */
}

.carousel-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  color: #333;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.carousel-nav-button:hover {
  background-color: #f5f5f5;
}

.carousel-nav-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.carousel-nav-button.prev {
  left: 10px;
}

.carousel-nav-button.next {
  right: 10px;
}

.carousel-product-card {
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.carousel-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.carousel-product-image {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}

.carousel-product-content {
  padding: 16px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.carousel-product-title {
  font-weight: 600;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.carousel-product-seller {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 8px;
}

.carousel-product-description {
  font-size: 0.875rem;
  color: #444;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.carousel-product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.carousel-product-price {
  font-weight: 700;
  font-size: 1.125rem;
}

.carousel-product-actions {
  display: flex;
  gap: 8px;
}

/* Animation for item transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.carousel-item-enter {
  animation: fadeIn 0.3s forwards;
}

/* Dark mode support */
.dark .carousel-nav-button {
  background-color: #333;
  color: #fff;
}

.dark .carousel-nav-button:hover {
  background-color: #444;
}

.dark .carousel-product-card {
  background-color: #222;
}

.dark .carousel-product-title {
  color: #fff;
}

.dark .carousel-product-seller {
  color: #aaa;
}

.dark .carousel-product-description {
  color: #ccc;
}

.dark .carousel-product-price {
  color: #fff;
}
