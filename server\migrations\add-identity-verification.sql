-- Migration: Add identity verification fields to users table
-- Date: 2024-12-19

-- Add identity verification columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS identity_verified BOOLEAN DEFAULT FALSE NOT NULL,
ADD COLUMN IF NOT EXISTS identity_verification_status TEXT DEFAULT 'none' NOT NULL,
ADD COLUMN IF NOT EXISTS identity_verification_submitted_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS identity_verification_approved_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS identity_verification_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS trust_score INTEGER DEFAULT 30 NOT NULL;

-- Update existing users to have the base trust score of 30
UPDATE users 
SET trust_score = 30 
WHERE trust_score IS NULL OR trust_score = 0;

-- Add check constraints
ALTER TABLE users 
ADD CONSTRAINT check_identity_verification_status 
CHECK (identity_verification_status IN ('none', 'pending', 'approved', 'rejected'));

ALTER TABLE users 
ADD CONSTRAINT check_trust_score_range 
CHECK (trust_score >= 0 AND trust_score <= 100);

-- Create index for performance on trust score queries
CREATE INDEX IF NOT EXISTS idx_users_trust_score ON users(trust_score);
CREATE INDEX IF NOT EXISTS idx_users_identity_verified ON users(identity_verified);
CREATE INDEX IF NOT EXISTS idx_users_identity_verification_status ON users(identity_verification_status);

-- Update products table to use user trust scores instead of hardcoded values
-- This will be handled in the application logic, but we can update existing products
UPDATE products 
SET trust_score = (
  SELECT COALESCE(u.trust_score, 30) 
  FROM users u 
  WHERE u.id = products.seller_id
)
WHERE EXISTS (
  SELECT 1 FROM users u WHERE u.id = products.seller_id
);
