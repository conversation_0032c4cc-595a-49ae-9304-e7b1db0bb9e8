import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Calculate trust score - all DasWos accounts have seller privileges built-in
    let trustScore = 0;

    // Base score for sales (0-30 points)
    const successfulSales = 0; // TODO: Query actual successful sales count
    const baseScore = Math.min(successfulSales, 30);
    trustScore += baseScore;

    // Account bonus (30 points) - all logged-in users get this
    const hasAccount = !!user; // User is logged in
    if (hasAccount) {
      trustScore += 30;
    }

    // Identity verification bonus (40 points)
    // For now, we'll set this to false since we're not using the seller verification system
    // TODO: Implement proper identity verification tracking
    const hasIdentityVerification = false;
    if (hasIdentityVerification) {
      trustScore += 40;
    }

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    return res.json({
      isVerified: hasAccount, // All logged-in users are considered "verified" (have an account)
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    });

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Serve the sell page
router.get('/sell', (req, res) => {
  res.sendFile('index.html', { root: 'client/dist' });
});

  return router;
}
