import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user with seller information
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email),
      with: {
        seller: true,
        sellerVerifications: true
      }
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is a seller
    if (!user.isSeller || !user.seller) {
      return res.json({
        isVerified: false,
        hasIdentityVerification: false,
        trustScore: 0,
        tier: 'New Seller',
        maxListings: 5,
        platformFee: '5%',
        canListInSafeSphere: false
      });
    }

    // Calculate trust score
    let trustScore = 0;

    // Base score (0-30 points)
    const successfulSales = 0; // TODO: Query actual successful sales count
    const baseScore = Math.min(successfulSales, 30);
    trustScore += baseScore;

    // Seller account bonus (30 points)
    const hasSellerAccount = !!user.seller;
    if (hasSellerAccount) {
      trustScore += 30;
    }

    // Identity verification bonus (40 points)
    const hasIdentityVerification = user.sellerVerifications?.some(
      v => v.type === 'identity' && v.status === 'approved'
    );
    if (hasIdentityVerification) {
      trustScore += 40;
    }

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    return res.json({
      isVerified: hasSellerAccount,
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    });

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Serve the sell page
router.get('/sell', (req, res) => {
  res.sendFile('index.html', { root: 'client/dist' });
});

  return router;
}
