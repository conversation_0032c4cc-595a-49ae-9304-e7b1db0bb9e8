import { Router } from 'express';
import { db } from '../db';
import { products, sellers, users } from '@shared/schema';
import { and, eq } from 'drizzle-orm';
import { isAuthenticated } from '../middleware/auth';
import { IStorage } from '../storage';

export default function createSellRoutes(storage: IStorage) {
  const router = Router();

// Submit identity verification
router.post('/api/user/verify-identity', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userId = req.user.id;
    console.log('Processing identity verification for user:', userId);

    // For testing purposes, we'll automatically approve the verification
    // In a real app, you would:
    // 1. Store the uploaded files
    // 2. Process the verification documents
    // 3. Update the user's verification status

    // Simulate storing the verification data
    const verificationData = {
      userId,
      fullName: req.body.fullName,
      email: req.body.email,
      phone: req.body.phone,
      dateOfBirth: req.body.dateOfBirth,
      address: req.body.address,
      status: 'approved', // Auto-approve for testing
      submittedAt: new Date(),
      approvedAt: new Date()
    };

    console.log('Auto-approving identity verification:', verificationData);

    // Store the verification in session for this user
    // In a real app, this would be stored in the database
    if (!req.session.userVerifications) {
      req.session.userVerifications = {};
    }
    req.session.userVerifications[userId] = verificationData;

    res.json({
      message: 'Identity verification submitted and approved!',
      status: 'approved',
      trustScoreIncrease: 40
    });

  } catch (error) {
    console.error('Error processing identity verification:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Get seller verification status
router.get('/api/seller/status', isAuthenticated, async (req, res) => {
  try {
    if (!req.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user information - simplified query without problematic relations
    const user = await db.query.users.findFirst({
      where: eq(users.email, req.user.email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Calculate trust score - all DasWos accounts have seller privileges built-in
    let trustScore = 0;

    // Base score for sales (0-30 points)
    const successfulSales = 0; // TODO: Query actual successful sales count
    const baseScore = Math.min(successfulSales, 30);
    trustScore += baseScore;

    // Account bonus (30 points) - all logged-in users get this
    const hasAccount = !!user; // User is logged in
    if (hasAccount) {
      trustScore += 30;
    }

    // Identity verification bonus (40 points)
    // Check if user has completed identity verification (stored in session for testing)
    const hasIdentityVerification = !!(req.session.userVerifications &&
      req.session.userVerifications[user.id] &&
      req.session.userVerifications[user.id].status === 'approved');

    if (hasIdentityVerification) {
      trustScore += 40;
    }

    // Debug logging
    console.log('Trust Score Calculation:', {
      user: !!user,
      hasAccount,
      baseScore,
      hasIdentityVerification,
      trustScore,
      userId: user?.id
    });

    // Determine seller tier and benefits
    let tier = 'New Seller';
    let maxListings = 5;
    let platformFee = '5%';
    let canListInSafeSphere = false;

    if (trustScore >= 85) {
      tier = 'Premium';
      maxListings = 200;
      platformFee = '1.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 70) {
      tier = 'Trusted';
      maxListings = 50;
      platformFee = '2.5%';
      canListInSafeSphere = true;
    } else if (trustScore >= 30) {
      tier = 'Verified';
      maxListings = 20;
      platformFee = '3.5%';
    }

    return res.json({
      isVerified: hasAccount, // All logged-in users are considered "verified" (have an account)
      hasIdentityVerification,
      trustScore,
      tier,
      maxListings,
      platformFee,
      canListInSafeSphere
    });

  } catch (error) {
    console.error('Error fetching seller status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Note: In development mode, the /sell route is handled by Vite's catch-all middleware
// In production mode, it's handled by the serveStatic function in vite.ts

  return router;
}
