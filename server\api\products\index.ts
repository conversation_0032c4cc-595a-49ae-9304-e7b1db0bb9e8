import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { prisma } from '../../../lib/prisma';
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Configure AWS S3
const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'daswos-uploads';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb',
    },
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    return handleCreateProduct(req, res);
  } else if (req.method === 'GET') {
    return handleGetProducts(req, res);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}

async function handleCreateProduct(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.email) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get user with seller information
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { seller: true }
    });

    if (!user || !user.isSeller || !user.seller) {
      return res.status(403).json({ message: 'Seller account required' });
    }

    // Parse form data
    const formData = req.body;
    const { title, description, price, quantity, category, tags, shipping } = formData;

    // Validate required fields
    if (!title || !description || !price || !quantity || !category || !shipping) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Handle file upload if present
    let imageUrl = '';
    if (req.body.image) {
      try {
        // In a real app, you would upload the file to S3 here
        // For now, we'll just generate a placeholder URL
        const fileName = `products/${uuidv4()}.jpg`;
        const uploadUrl = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileName}`;
        
        // In a real implementation, you would upload the file to S3
        // const fileBuffer = Buffer.from(req.body.image, 'base64');
        // const command = new PutObjectCommand({
        //   Bucket: BUCKET_NAME,
        //   Key: fileName,
        //   Body: fileBuffer,
        //   ContentType: 'image/jpeg',
        //   ACL: 'public-read'
        // });
        // await s3Client.send(command);
        
        imageUrl = uploadUrl;
      } catch (error) {
        console.error('Error uploading image:', error);
        return res.status(500).json({ message: 'Error uploading image' });
      }
    }

    // Create product in database
    const product = await prisma.product.create({
      data: {
        title,
        description,
        price: Math.round(parseFloat(price) * 100), // Convert to cents
        imageUrl: imageUrl || '/placeholder-product.jpg',
        sellerId: user.id,
        sellerName: user.seller.businessName || user.name || 'Unknown Seller',
        sellerVerified: user.seller.verificationStatus === 'approved',
        sellerType: 'merchant',
        trustScore: user.seller.trustScore || 0,
        tags: tags ? tags.split(',').map((tag: string) => tag.trim()) : [],
        shipping,
        quantity: parseInt(quantity, 10),
        categoryId: category,
        aiAttributes: {},
        searchVector: ''
      }
    });

    return res.status(201).json(product);

  } catch (error) {
    console.error('Error creating product:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGetProducts(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { page = '1', limit = '20', category, search } = req.query;
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    const where: any = {};
    
    if (category) {
      where.categoryId = category;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search as string, mode: 'insensitive' } },
        { description: { contains: search as string, mode: 'insensitive' } },
        { tags: { has: search as string } }
      ];
    }

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limitNum,
        include: {
          seller: true,
          category: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.product.count({ where })
    ]);

    return res.json({
      data: products,
      pagination: {
        total,
        page: pageNum,
        totalPages: Math.ceil(total / limitNum)
      }
    });

  } catch (error) {
    console.error('Error fetching products:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
